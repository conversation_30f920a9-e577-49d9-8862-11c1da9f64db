openapi: 3.0.0
info:
  version: 3.0.0
  title: braze
tags:
  - name: braze
    description: braze api
security:
  - api_key: []
paths:
  /users/track:
    post:
      tags:
        - braze
      summary: track user
      operationId: trackUser
      description: track user data
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TrackUserRequest'
        description: track user request
        required: true
      responses:
        '200':
          description: OK
        default:
          $ref: '#/components/schemas/ErrorResponse'
  /users/delete:
    post:
      tags:
        - braze
      summary: delete user
      operationId: deleteUser
      description: delete user by email address
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteUserRequest'
        description: delete user profile
        required: true
      responses:
        '200':
          description: OK
        default:
          $ref: '#/components/schemas/ErrorResponse'
  /users/export/ids:
    post:
      tags:
        - braze
      summary: export user
      operationId: exportUser
      description: export user profile information
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExportUserRequest'
        description: export user request
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExportUserResponse'
        default:
          $ref: '#/components/schemas/ErrorResponse'
  /canvas/trigger/send:
    post:
      tags:
        - braze
      summary: canvas trigger instant
      operationId: canvasTrigger
      description: canvas trigger instant
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CanvasTriggerRequest'
        description: canvas trigger request
        required: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CanvasTriggerResponse'
        default:
          $ref: '#/components/schemas/ErrorResponse'
  /catalogs/{catalog_name}/items:
    put:
      tags:
        - braze
      summary: replace multiple catalog items
      operationId: updateCatalogItems
      description: replace multiple items in your catalog
      parameters:
        - name: catalog_name
          in: path
          required: true
          description: Name of the catalog
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCatalogItemsRequest'
        description: update catalog items request
        required: true
      responses:
        '202':
          description: Accepted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCatalogItemsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
components:
  schemas:
    TrackUserRequest:
      type: object
      properties:
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/Attribute'
    Attribute:
      type: object
      properties:
        external_id:
          type: string
        braze_id:
          type: string
        email:
          type: string
        _update_existing_only:
          type: boolean
        email_subscribe:
          type: string
          enum: [ opted_in, unsubscribed, subscribed ]
        push_token_import:
          type: boolean
        messages:
          $ref: '#/components/schemas/AddMessage'
        favourites:
          $ref: '#/components/schemas/AddRemoveFavourite'
        reactions_v1:
          $ref: '#/components/schemas/AddReaction'
        ratings:
          $ref: '#/components/schemas/AddRating'
        last_replied_advert_id:
          type: integer
          format: int64
        last_favourited_advert_id:
          type: integer
          format: int64
        last_viewed_advert_id:
          type: integer
          format: int64
        last_rated_advert_id:
          type: integer
          format: int64
        first_name:
          type: string
        user_status:
          type: string
        encrypted_user_id:
          type: string
        registration_date:
          type: string
          format: date-time
        activation_date:
          type: string
          format: date-time
        marketing_permission:
          type: boolean
      required:
        - external_id
    AddMessage:
      type: object
      properties:
        $add:
          type: array
          items:
            $ref: '#/components/schemas/Message'
    Message:
      type: object
      properties:
        replied_advert_id:
          type: integer
          format: int64
        replied_date_v1:
          $ref: '#/components/schemas/TimeValue'
        message_read:
          type: boolean
        conversation_id:
          type: string
        direction:
          type: string
        buyer_user_id:
          type: integer
          format: int64
        seller_user_id:
          type: integer
          format: int64
    AddRemoveFavourite:
      type: object
      properties:
        $add:
          type: array
          items:
            $ref: '#/components/schemas/AddFavourite'
        $remove:
          type: array
          items:
            $ref: '#/components/schemas/RemoveFavourite'
    AddFavourite:
      type: object
      properties:
        favourited_advert_id:
          type: integer
          format: int64
        favourited_date_v1:
          $ref: '#/components/schemas/TimeValue'
    TimeValue:
      type: object
      properties:
        $time:
          type: string
          format: date-time
    RemoveFavourite:
      type: object
      properties:
        $identifier_key:
          type: string
        $identifier_value:
          type: integer
          format: int64
    AddReaction:
      type: object
      properties:
        $add:
          type: array
          items:
            $ref: '#/components/schemas/Reaction'
    Reaction:
      type: object
      properties:
        viewed_advert_id:
          type: integer
          format: int64
        viewed_event_date_v1:
          $ref: '#/components/schemas/TimeValue'
    AddRating:
      type: object
      properties:
        $add:
          type: array
          items:
            $ref: '#/components/schemas/Rating'
    Rating:
      type: object
      properties:
        rated_advert_id:
          type: integer
          format: int64
        rated_date_v1:
          $ref: '#/components/schemas/TimeValue'
        user_rating:
          type: string
        number_of_ratings_received:
          type: integer
        number_of_ratings_given:
          type: integer
        buyer_seller:
          type: string
    DeleteUserRequest:
      type: object
      properties:
        email_addresses:
          type: array
          items:
            $ref: '#/components/schemas/EmailAddress'
    EmailAddress:
      type: object
      properties:
        email:
          type: string
        prioritization:
          type: array
          items:
            $ref: '#/components/schemas/Prioritization'
    Prioritization:
      type: string
      enum:
        - identified
        - unidentified
        - most_recently_updated
    ExportUserRequest:
      type: object
      properties:
        email_address:
          type: string
    ExportUserResponse:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
    User:
      type: object
      properties:
        braze_id:
          type: string
        email:
          type: string
        external_id:
          type: string
        first_name:
          type: string
        created_at:
          type: string
          format: date-time
        phone:
          type: string
        email_subscribe:
          type: string
        user_aliases:
          type: array
          items:
            $ref: '#/components/schemas/UserAlias'
        apps:
          type: array
          items:
            $ref: '#/components/schemas/UserApp'
        custom_attributes:
          type: object
          additionalProperties: true
    UserAlias:
      type: object
      properties:
        alias_name:
          type: string
        alias_label:
          type: string
    UserApp:
      type: object
      properties:
        name:
          type: string
        platform:
          type: string
        version:
          type: string
        sessions:
          type: integer
        first_used:
          type: string
        last_used:
          type: string
        created_at:
          type: string
        custom_attributes:
          type: array
          items:
            $ref: '#/components/schemas/CustomAttribute'
    CustomAttribute:
      type: object
      properties:
        key:
          type: string
        value:
          type: string
    CanvasTriggerRequest:
      type: object
      properties:
        canvas_id:
          type: string
        recipients:
          type: array
          items:
            $ref: '#/components/schemas/Recipient'
        broadcast:
          type: boolean
    CanvasTriggerResponse:
      type: object
      properties:
        message:
          type: string
        notice:
          type: string
        dispatch_id:
          type: string
    Recipient:
      type: object
      properties:
        external_user_id:
          type: string
        email:
          type: string
        prioritization:
          type: array
          items:
            type: string
            enum: [identified, unidentified, most_recently_updated, least_recently_updated]
        send_to_existing_only:
          type: boolean
        canvas_entry_properties:
          type: object
          additionalProperties: true
        attributes:
          type: object
          properties:
            email:
              type: string
    ErrorResponse:
      type: object
      properties:
        message:
          type: string
        errors:
          type: array
          items:
            $ref: '#/components/schemas/Error'
    Error:
      type: object
    UpdateCatalogItemsRequest:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/CatalogItem'
          description: An array that contains item objects. Each object must have an ID.
      required:
        - items
    UpdateCatalogItemsResponse:
      type: object
      properties:
        message:
          type: string
    CatalogItem:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the catalog item
        CRM_ID:
          type: string
        USER_ID:
          type: string
        TITLE:
          type: string
        DESCRIPTION:
          type: string
        PRICE:
          type: number
          format: float
        PRICE_QUALIFIER:
          type: string
        NUMBER_IMAGES:
          type: integer
        IMAGE_URL:
          type: string
          format: uri
        ADVERT_URL:
          type: string
          format: uri
        CREATED_DATE:
          type: string
          format: date-time
        MODIFIED_DATE:
          type: string
          format: date-time
        SCHEDULED_END_DATE:
          type: string
          format: date-time
        STATUS:
          type: string
        L1_CATEGORY:
          type: string
        L2_CATEGORY:
          type: string
        L3_CATEGORY:
          type: string
        L4_CATEGORY:
          type: string
        L5_CATEGORY:
          type: string
        L1_LOCATION:
          type: string
        L2_LOCATION:
          type: string
        L3_LOCATION:
          type: string
        L4_LOCATION:
          type: string
        L5_LOCATION:
          type: string
        TOP_ADVERT_3:
          type: boolean
        TOP_ADVERT_7:
          type: boolean
        TOP_ADVERT_14:
          type: boolean
        FEATURED_URL:
          type: string
          format: uri
        HOMEPAGE:
          type: boolean
        URGENT:
          type: boolean
        SEARCH_IMPRESSIONS:
          type: integer
        VIP_VIEWS:
          type: integer
        BUMP_UPS:
          type: integer
        REPLIES:
          type: integer
        POSTCODE:
          type: string
        OUTCODE:
          type: string
        PRO:
          type: boolean
        STATS_DATE:
          type: string
          format: date-time
        POSTING_SINCE:
          type: string
          format: date-time
        FIRSTNAME:
          type: string
        EMAIL:
          type: string
          format: email
        CATEGORY_ID:
          type: string
        ADVERT_ID:
          type: string
        PUBLISHED_DATE:
          type: string
          format: date-time
      required:
        - id
